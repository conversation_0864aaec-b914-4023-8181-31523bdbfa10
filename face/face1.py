import cv2
import numpy as np
import os
import argparse
import json
from pathlib import Path

class CommandLineSmileDetector:
    def __init__(self, verbose=False):
        self.verbose = verbose
        
        # 加载人脸和笑容检测器
        try:
            self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
            self.smile_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_smile.xml')
            if self.verbose:
                print("✓ 检测器加载成功")
        except Exception as e:
            print(f"✗ 检测器加载失败: {e}")
            exit(1)
    
    def analyze_smile_intensity(self, gray_face_roi):
        """
        分析笑容强度（更精细的评分算法）
        """
        # 检测笑容区域
        smiles = self.smile_cascade.detectMultiScale(
            gray_face_roi,
            scaleFactor=1.8,
            minNeighbors=25,
            minSize=(20, 20)
        )
        
        if len(smiles) == 0:
            return 0, 0, []  # 评分, 检测到的笑容数, 笑容区域列表
        
        # 计算笑容区域的总体特征
        total_area = 0
        smile_regions = []
        
        for (x, y, w, h) in smiles:
            area = w * h
            total_area += area
            smile_regions.append((x, y, w, h))
            
            # 分析嘴巴区域的纹理特征（简单的边缘检测）
            smile_roi = gray_face_roi[y:y+h, x:x+w]
            if smile_roi.size > 0:
                # 计算边缘强度（笑容通常有更明显的边缘）
                edges = cv2.Canny(smile_roi, 50, 150)
                edge_density = np.sum(edges > 0) / edges.size if edges.size > 0 else 0
                total_area += area * edge_density * 10
        
        # 基础评分基于检测到的笑容区域
        base_score = min(80, len(smiles) * 20 + total_area / 100)
        
        # 添加嘴巴形状分析
        mouth_analysis_score = self.analyze_mouth_shape(gray_face_roi)
        
        # 综合评分
        final_score = min(100, base_score + mouth_analysis_score * 20)
        
        return final_score, len(smiles), smile_regions
    
    def analyze_mouth_shape(self, face_roi):
        """
        分析嘴巴形状特征
        """
        try:
            height, width = face_roi.shape
            
            # 假设嘴巴在脸的下半部分
            mouth_region = face_roi[int(height*0.6):, :]
            
            if mouth_region.size == 0:
                return 0
            
            # 应用二值化和边缘检测
            _, thresh = cv2.threshold(mouth_region, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            edges = cv2.Canny(thresh, 50, 150)
            
            # 计算水平边缘和垂直边缘的比例（笑容通常有更多水平边缘）
            horizontal_kernel = np.array([[1, 1, 1], [0, 0, 0], [-1, -1, -1]])
            vertical_kernel = np.array([[1, 0, -1], [1, 0, -1], [1, 0, -1]])
            
            horizontal_edges = cv2.filter2D(mouth_region, -1, horizontal_kernel)
            vertical_edges = cv2.filter2D(mouth_region, -1, vertical_kernel)
            
            horizontal_strength = np.mean(np.abs(horizontal_edges))
            vertical_strength = np.mean(np.abs(vertical_edges))
            
            if vertical_strength > 0:
                ratio = horizontal_strength / vertical_strength
                return min(1.0, ratio / 3.0)  # 归一化到0-1
            return 0
            
        except Exception as e:
            if self.verbose:
                print(f"嘴巴形状分析失败: {e}")
            return 0
    
    def detect_smile_score(self, image_path):
        """
        检测单张图片的笑容评分
        """
        if not os.path.exists(image_path):
            return {"error": f"图片不存在: {image_path}"}
        
        img = cv2.imread(image_path)
        if img is None:
            return {"error": f"无法读取图片: {image_path}"}
        
        # 调整图片大小以提高处理速度
        height, width = img.shape[:2]
        if max(height, width) > 1000:
            scale = 1000 / max(height, width)
            new_size = (int(width * scale), int(height * scale))
            img = cv2.resize(img, new_size)
        
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # 检测人脸
        faces = self.face_cascade.detectMultiScale(
            gray, 
            scaleFactor=1.1, 
            minNeighbors=5, 
            minSize=(50, 50)
        )
        
        if len(faces) == 0:
            return {"error": "未检测到人脸"}
        
        results = {
            "image_path": image_path,
            "faces_detected": len(faces),
            "faces": []
        }
        
        for i, (x, y, w, h) in enumerate(faces):
            face_roi = gray[y:y+h, x:x+w]
            
            # 分析笑容
            smile_score, smile_count, smile_regions = self.analyze_smile_intensity(face_roi)
            is_smiling = smile_score > 45  # 阈值可调整
            
            face_result = {
                "face_id": i + 1,
                "position": {"x": int(x), "y": int(y), "width": int(w), "height": int(h)},
                "smile_score": round(float(smile_score), 2),
                "is_smiling": bool(is_smiling),
                "smile_regions_detected": int(smile_count),
                "confidence": "high" if smile_score > 70 else "medium" if smile_score > 40 else "low"
            }
            
            results["faces"].append(face_result)
            
            if self.verbose:
                status = "微笑" if is_smiling else "未微笑"
                print(f"人脸 {i+1}: 评分 {smile_score:.1f}% - {status} - 检测到{smile_count}个笑容区域")
        
        return results
    
    def process_directory(self, directory_path, output_format="text"):
        """
        处理整个目录的图片
        """
        directory_path = Path(directory_path)
        if not directory_path.exists():
            return {"error": f"目录不存在: {directory_path}"}
        
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
        image_files = [f for f in directory_path.iterdir() 
                      if f.suffix.lower() in image_extensions and f.is_file()]
        
        if not image_files:
            return {"error": f"目录中没有找到图片文件: {directory_path}"}
        
        all_results = {
            "directory": str(directory_path),
            "total_images": len(image_files),
            "processed_images": 0,
            "results": []
        }
        
        for image_file in image_files:
            if self.verbose:
                print(f"处理图片: {image_file.name}")
            
            result = self.detect_smile_score(str(image_file))
            if "error" not in result:
                all_results["processed_images"] += 1
                all_results["results"].append(result)
        
        return all_results

def print_results(results, output_format="text"):
    """
    格式化输出结果
    """
    if "error" in results:
        print(f"错误: {results['error']}")
        return
    
    if "directory" in results:
        # 目录处理结果
        print(f"目录: {results['directory']}")
        print(f"总图片数: {results['total_images']}")
        print(f"成功处理: {results['processed_images']}")
        print("-" * 50)
        
        for result in results["results"]:
            print(f"\n图片: {Path(result['image_path']).name}")
            print(f"检测到人脸: {result['faces_detected']}")
            
            for face in result["faces"]:
                status = "微笑" if face["is_smiling"] else "未微笑"
                print(f"  人脸 {face['face_id']}: {face['smile_score']}% ({status})")
    
    else:
        # 单张图片结果
        print(f"图片: {Path(results['image_path']).name}")
        print(f"检测到人脸: {results['faces_detected']}")
        
        for face in results["faces"]:
            status = "微笑" if face["is_smiling"] else "未微笑"
            confidence = face["confidence"]
            print(f"人脸 {face['face_id']}:")
            print(f"  位置: ({face['position']['x']}, {face['position']['y']}) "
                  f"大小: {face['position']['width']}x{face['position']['height']}")
            print(f"  笑容评分: {face['smile_score']}%")
            print(f"  状态: {status}")
            print(f"  置信度: {confidence}")
            print(f"  检测到笑容区域: {face['smile_regions_detected']}个")

def main():
    parser = argparse.ArgumentParser(description="人脸笑容评分系统")
    parser.add_argument("input", help="图片文件路径或目录路径")
    parser.add_argument("--output", "-o", choices=["text", "json"], default="text", 
                       help="输出格式: text (默认) 或 json")
    parser.add_argument("--verbose", "-v", action="store_true", 
                       help="显示详细处理信息")
    
    args = parser.parse_args()
    
    # 创建检测器
    detector = CommandLineSmileDetector(verbose=args.verbose)
    
    # 检查输入是文件还是目录
    input_path = Path(args.input)
    
    if input_path.is_file():
        # 处理单张图片
        results = detector.detect_smile_score(str(input_path))
    elif input_path.is_dir():
        # 处理目录
        results = detector.process_directory(str(input_path), args.output)
    else:
        print(f"错误: 路径不存在 {args.input}")
        return
    
    # 输出结果
    if args.output == "json":
        print(json.dumps(results, ensure_ascii=False, indent=2))
    else:
        print_results(results, args.output)

# 简单使用示例（不依赖命令行参数）
def simple_example():
    """
    简单使用示例
    """
    detector = CommandLineSmileDetector(verbose=True)
    
    # 检测单张图片
    image_path = "test_face.jpg"  # 替换为你的图片路径
    
    if os.path.exists(image_path):
        print("=== 单张图片检测 ===")
        results = detector.detect_smile_score(image_path)
        print_results(results)
    else:
        print(f"测试图片不存在: {image_path}")
        
        # 使用当前目录下的第一张图片
        image_files = [f for f in os.listdir('.') 
                      if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
        
        if image_files:
            test_image = image_files[0]
            print(f"使用图片: {test_image}")
            results = detector.detect_smile_score(test_image)
            print_results(results)
        else:
            print("当前目录下没有找到图片文件")

if __name__ == "__main__":
    # 如果有命令行参数，使用argparse版本
    import sys
    if len(sys.argv) > 1:
        main()
    else:
        # 否则使用简单示例
        simple_example()