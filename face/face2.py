import cv2
import numpy as np
import mediapipe as mp
import os
import argparse
from pathlib import Path

class ComprehensiveSmileScorer:
    def __init__(self, verbose=False):
        self.verbose = verbose
        self.mp_face_mesh = mp.solutions.face_mesh
        self.mp_face_detection = mp.solutions.face_detection
        
        # 初始化检测器
        self.face_detection = self.mp_face_detection.FaceDetection(
            model_selection=1,
            min_detection_confidence=0.3
        )
        
        self.face_mesh = self.mp_face_mesh.FaceMesh(
            static_image_mode=True,
            max_num_faces=10,
            refine_landmarks=True,
            min_detection_confidence=0.3,
            min_tracking_confidence=0.3
        )
        
        # 嘴巴关键点索引（MediaPipe 468点模型）
        self.LIPS_OUTER = [61, 146, 91, 181, 84, 17, 314, 405, 320, 307, 375, 291]
        self.LIPS_INNER = [78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308, 415]
        self.ALL_LIPS = self.LIPS_OUTER + self.LIPS_INNER
        
        if self.verbose:
            print("✓ 综合笑容评分器加载成功")

    def enhance_image(self, image):
        """图像增强处理"""
        # 对比度增强
        lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        cl = clahe.apply(l)
        enhanced_lab = cv2.merge((cl, a, b))
        enhanced = cv2.cvtColor(enhanced_lab, cv2.COLOR_LAB2BGR)
        return enhanced

    def detect_all_faces(self, image):
        """综合人脸检测"""
        h, w = image.shape[:2]
        faces = []
        
        # MediaPipe人脸检测
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        results = self.face_detection.process(rgb_image)
        
        if results.detections:
            for detection in results.detections:
                bbox = detection.location_data.relative_bounding_box
                x = int(bbox.xmin * w)
                y = int(bbox.ymin * h)
                width = int(bbox.width * w)
                height = int(bbox.height * h)
                
                # 确保边界框有效
                x, y = max(0, x), max(0, y)
                width = min(w - x, width)
                height = min(h - y, height)
                
                if width > 20 and height > 20:
                    confidence = detection.score[0]
                    faces.append((x, y, width, height, confidence, "mediapipe"))
        
        # 备用：OpenCV Haar级联
        if not faces:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
            cv_faces = face_cascade.detectMultiScale(gray, 1.1, 4, minSize=(30, 30))
            
            for (x, y, w, h) in cv_faces:
                faces.append((x, y, w, h, 0.5, "opencv"))
        
        return faces

    def calculate_comprehensive_smile_score(self, landmarks, face_region, image_shape):
        """综合笑容评分算法（0-100分）"""
        if landmarks is None:
            return self.estimate_smile_from_face(face_region, image_shape)
        
        h, w = image_shape[:2]
        face_x, face_y, face_w, face_h = face_region
        
        # 1. 提取嘴巴关键点
        mouth_points = []
        for idx in self.ALL_LIPS:
            if idx < len(landmarks.landmark):
                lm = landmarks.landmark[idx]
                mouth_points.append([lm.x * w, lm.y * h])
        
        if len(mouth_points) < 8:
            return self.estimate_smile_from_face(face_region, image_shape)
        
        mouth_points = np.array(mouth_points)
        
        # 2. 基础几何特征
        mouth_width = np.max(mouth_points[:, 0]) - np.min(mouth_points[:, 0])
        mouth_height = np.max(mouth_points[:, 1]) - np.min(mouth_points[:, 1])
        
        if mouth_width == 0:
            return 0
        
        aspect_ratio = mouth_height / mouth_width
        
        # 3. 详细特征分析
        features = self.analyze_detailed_smile_features(landmarks, image_shape)
        
        # 4. 综合评分（权重可调整）
        smile_score = 0
        
        # 特征1: 嘴巴宽高比（笑容时嘴巴变宽）权重: 25%
        ratio_score = max(0, 1.5 - aspect_ratio * 3) * 25
        smile_score += min(25, ratio_score)
        
        # 特征2: 嘴巴相对宽度（笑容时嘴巴张大）权重: 20%
        relative_width = mouth_width / face_w
        width_score = min(1.0, relative_width / 0.6) * 20  # 假设正常嘴巴占脸宽的60%
        smile_score += width_score
        
        # 特征3: 嘴角上扬程度 权重: 20%
        corner_score = features.get('corner_curve', 0) * 20
        smile_score += corner_score
        
        # 特征4: 嘴唇分离程度 权重: 15%
        separation_score = features.get('lip_separation', 0) * 15
        smile_score += separation_score
        
        # 特征5: 嘴巴弯曲度 权重: 10%
        curvature_score = features.get('mouth_curvature', 0) * 10
        smile_score += curvature_score
        
        # 特征6: 对称性 权重: 10%
        symmetry_score = features.get('symmetry', 0) * 10
        smile_score += symmetry_score
        
        # 确保分数在0-100之间
        final_score = max(0, min(100, smile_score))
        
        if self.verbose:
            print(f"    详细评分: 宽高比{ratio_score:.1f} + 宽度{width_score:.1f} + "
                  f"嘴角{corner_score:.1f} + 分离{separation_score:.1f} = {final_score:.1f}")
        
        return final_score

    def estimate_smile_from_face(self, face_region, image_shape):
        """当无法获取详细特征时的估计评分"""
        face_x, face_y, face_w, face_h = face_region
        h, w = image_shape[:2]
        
        # 基于人脸宽高比的简单估计
        face_ratio = face_w / face_h
        
        # 正常人脸宽高比约0.7-0.8，笑容时可能略宽
        if face_ratio > 0.85:
            score = min(70, 40 + (face_ratio - 0.85) * 200)  # 较宽的人脸
        elif face_ratio > 0.75:
            score = 30 + (face_ratio - 0.75) * 100  # 正常范围
        else:
            score = max(10, face_ratio * 40)  # 较瘦的人脸
        
        return min(80, score)  # 估计方法最高给80分

    def analyze_detailed_smile_features(self, landmarks, image_shape):
        """分析详细的笑容特征"""
        h, w = image_shape[:2]
        features = {}
        
        try:
            # 关键点索引
            LEFT_CORNER = 61
            RIGHT_CORNER = 291
            UPPER_LIP_TOP = 13
            LOWER_LIP_BOTTOM = 14
            UPPER_LIP_CENTER = 0
            LOWER_LIP_CENTER = 17
            
            # 1. 嘴角上扬分析
            if all(idx < len(landmarks.landmark) for idx in [LEFT_CORNER, RIGHT_CORNER, UPPER_LIP_TOP]):
                left_corner_y = landmarks.landmark[LEFT_CORNER].y
                right_corner_y = landmarks.landmark[RIGHT_CORNER].y
                upper_lip_y = landmarks.landmark[UPPER_LIP_TOP].y
                
                # 嘴角相对于上唇的位置（值为正表示嘴角低于上唇，即微笑）
                left_curve = (left_corner_y - upper_lip_y) * h
                right_curve = (right_corner_y - upper_lip_y) * h
                
                # 归一化处理
                mouth_ref_height = h * 0.05  # 参考高度
                avg_curve = (abs(left_curve) + abs(right_curve)) / 2
                features['corner_curve'] = min(1.0, avg_curve / mouth_ref_height)
            
            # 2. 嘴唇分离程度
            if all(idx < len(landmarks.landmark) for idx in [UPPER_LIP_CENTER, LOWER_LIP_CENTER]):
                upper_y = landmarks.landmark[UPPER_LIP_CENTER].y * h
                lower_y = landmarks.landmark[LOWER_LIP_CENTER].y * h
                separation = lower_y - upper_y
                features['lip_separation'] = min(1.0, separation / (h * 0.03))
            
            # 3. 嘴巴弯曲度
            mouth_points = []
            for idx in [61, 84, 17, 314, 405, 291]:  # 外唇关键点
                if idx < len(landmarks.landmark):
                    mouth_points.append([landmarks.landmark[idx].x, landmarks.landmark[idx].y])
            
            if len(mouth_points) >= 3:
                mouth_points = np.array(mouth_points)
                # 计算弯曲度（笑容时嘴巴呈弧形）
                x_coords = mouth_points[:, 0]
                y_coords = mouth_points[:, 1]
                
                # 拟合二次曲线
                if len(x_coords) > 2:
                    coeffs = np.polyfit(x_coords, y_coords, 2)
                    curvature = abs(coeffs[0]) * 1000  # 曲率系数
                    features['mouth_curvature'] = min(1.0, curvature)
            
            # 4. 对称性分析
            if len(mouth_points) >= 6:
                left_half = mouth_points[:len(mouth_points)//2]
                right_half = mouth_points[len(mouth_points)//2:]
                
                if len(left_half) == len(right_half):
                    # 计算左右对称性
                    right_flipped = np.copy(right_half)
                    right_flipped[:, 0] = 1 - right_flipped[:, 0]  # 水平翻转
                    
                    differences = np.abs(left_half - right_flipped)
                    symmetry = 1 - np.mean(differences) / 0.1  # 归一化
                    features['symmetry'] = max(0, min(1.0, symmetry))
                    
        except Exception as e:
            if self.verbose:
                print(f"    特征分析错误: {e}")
        
        return features

    def get_smile_level(self, score):
        """根据分数返回笑容级别"""
        if score >= 90:
            return "开怀大笑", "😄"
        elif score >= 75:
            return "明显微笑", "😊"
        elif score >= 60:
            return "轻微微笑", "🙂"
        elif score >= 40:
            return "自然表情", "😐"
        elif score >= 20:
            return "严肃表情", "😶"
        else:
            return "紧绷表情", "😬"

    def analyze_image(self, image_path):
        """分析单张图片"""
        if not os.path.exists(image_path):
            return {"error": f"图片不存在: {image_path}"}
        
        img = cv2.imread(image_path)
        if img is None:
            return {"error": f"无法读取图片: {image_path}"}
        
        # 图像增强
        enhanced_img = self.enhance_image(img)
        h, w = img.shape[:2]
        
        # 人脸检测
        faces = self.detect_all_faces(enhanced_img)
        
        if not faces:
            return {"error": "未检测到人脸", "suggestion": "尝试使用更清晰、正面的人脸图片"}
        
        # 人脸网格分析
        rgb_img = cv2.cvtColor(enhanced_img, cv2.COLOR_BGR2RGB)
        mesh_results = self.face_mesh.process(rgb_img)
        
        results = {
            "image_path": image_path,
            "image_size": f"{w}x{h}",
            "faces_detected": len(faces),
            "analysis": []
        }
        
        for i, (x, y, w, h, confidence, method) in enumerate(faces):
            # 匹配人脸网格
            landmarks = None
            if mesh_results.multi_face_landmarks:
                for mesh_landmarks in mesh_results.multi_face_landmarks:
                    # 简单的中心点匹配
                    mesh_points = [(lm.x * w, lm.y * h) for lm in mesh_landmarks.landmark[:10]]
                    if mesh_points:
                        center_x = np.mean([p[0] for p in mesh_points])
                        center_y = np.mean([p[1] for p in mesh_points])
                        if (x <= center_x <= x + w and y <= center_y <= y + h):
                            landmarks = mesh_landmarks
                            break
            
            # 计算笑容评分
            smile_score = self.calculate_comprehensive_smile_score(
                landmarks, (x, y, w, h), img.shape
            )
            
            # 获取笑容级别
            level, emoji = self.get_smile_level(smile_score)
            is_smiling = smile_score >= 50  # 50分以上认为是微笑
            
            face_result = {
                "face_id": i + 1,
                "position": {"x": x, "y": y, "width": w, "height": h},
                "detection_method": method,
                "detection_confidence": round(confidence, 3),
                "smile_score": round(smile_score, 1),
                "smile_level": level,
                "emoji": emoji,
                "is_smiling": is_smiling,
                "detailed_method": "mediapipe_mesh" if landmarks else "geometric_estimate"
            }
            
            results["analysis"].append(face_result)
            
            if self.verbose:
                status = "微笑" if is_smiling else "非微笑"
                print(f"人脸 {i+1}: {smile_score:.1f}分 - {level}{emoji} - {status}")
        
        return results

def print_detailed_results(results):
    """打印详细结果"""
    if "error" in results:
        print(f"❌ 错误: {results['error']}")
        if "suggestion" in results:
            print(f"💡 建议: {results['suggestion']}")
        return
    
    print(f"📷 图片: {Path(results['image_path']).name}")
    print(f"📐 尺寸: {results['image_size']}")
    print(f"👥 检测到: {results['faces_detected']} 个人脸")
    print("=" * 70)
    
    for face in results["analysis"]:
        print(f"\n人脸 {face['face_id']}:")
        print(f"  📍 位置: ({face['position']['x']}, {face['position']['y']})")
        print(f"  📏 大小: {face['position']['width']}x{face['position']['height']}")
        print(f"  🎯 检测方法: {face['detection_method']} (置信度: {face['detection_confidence']})")
        print(f"  😊 笑容评分: {face['smile_score']}分 / 100分")
        print(f"  📊 笑容级别: {face['smile_level']} {face['emoji']}")
        print(f"  🔍 分析方式: {face['detailed_method']}")
        print(f"  💫 状态: {'微笑 😊' if face['is_smiling'] else '非微笑 😐'}")

def main():
    parser = argparse.ArgumentParser(description="综合笑容评分系统 (0-100分)")
    parser.add_argument("image_path", help="图片文件路径")
    parser.add_argument("--verbose", "-v", action="store_true", 
                       help="显示详细处理信息")
    
    args = parser.parse_args()
    
    scorer = ComprehensiveSmileScorer(verbose=args.verbose)
    results = scorer.analyze_image(args.image_path)
    
    print_detailed_results(results)

def batch_test():
    """批量测试当前目录图片"""
    print("=== 笑容评分批量测试 ===\n")
    
    scorer = ComprehensiveSmileScorer(verbose=True)
    test_images = [f for f in os.listdir('.') 
                  if f.lower().endswith(('.jpg', '.jpeg', '.png', '.webp'))]
    
    if not test_images:
        print("当前目录没有图片文件")
        return
    
    print(f"找到 {len(test_images)} 张测试图片\n")
    
    for img_path in test_images:
        print(f"🔍 分析图片: {img_path}")
        print("-" * 50)
        
        results = scorer.analyze_image(img_path)
        
        if "error" in results:
            print(f"❌ {results['error']}\n")
        else:
            for face in results["analysis"]:
                print(f"   人脸 {face['face_id']}: {face['smile_score']}分 - {face['smile_level']} {face['emoji']}")
            print()

if __name__ == "__main__":
    # 依赖检查
    try:
        import mediapipe as mp
        import cv2
        print("✓ 依赖检查通过")
    except ImportError as e:
        print(f"❌ 依赖缺失: {e}")
        exit(1)
    
    # 运行程序
    import sys
    if len(sys.argv) > 1:
        main()
    else:
        batch_test()