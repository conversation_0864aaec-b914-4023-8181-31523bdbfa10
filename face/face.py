import cv2
import dlib
import numpy as np
import matplotlib.pyplot as plt
from sklearn.svm import SVC
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score
import os
import pickle

class SmileDetector:
    def __init__(self, predictor_path="shape_predictor_68_face_landmarks.dat"):
        """
        初始化笑容检测器
        predictor_path: dlib的人脸关键点检测器路径
        """
        # 初始化dlib的人脸检测器和关键点检测器
        self.detector = dlib.get_frontal_face_detector()
        self.predictor = dlib.shape_predictor(predictor_path)
        
        # 初始化SVM分类器
        self.svm_classifier = SVC(kernel='linear', probability=True)
        self.scaler = StandardScaler()
        self.is_trained = False
        
        # 嘴巴关键点索引（根据68点模型）
        self.mouth_points = list(range(48, 68))
    
    def extract_mouth_features(self, image, landmarks):
        """
        从嘴巴关键点提取特征
        """
        # 获取嘴巴区域的关键点坐标
        mouth_coords = []
        for i in self.mouth_points:
            x = landmarks.part(i).x
            y = landmarks.part(i).y
            mouth_coords.append((x, y))
        
        mouth_coords = np.array(mouth_coords)
        
        # 计算嘴巴宽度和高度
        mouth_width = np.max(mouth_coords[:, 0]) - np.min(mouth_coords[:, 0])
        mouth_height = np.max(mouth_coords[:, 1]) - np.min(mouth_coords[:, 1])
        
        # 计算嘴巴的宽高比
        aspect_ratio = mouth_height / mouth_width if mouth_width > 0 else 0
        
        # 计算嘴巴关键点之间的角度和距离特征
        features = [mouth_width, mouth_height, aspect_ratio]
        
        # 添加关键点之间的相对距离
        for i in range(len(mouth_coords)):
            for j in range(i+1, len(mouth_coords)):
                distance = np.linalg.norm(mouth_coords[i] - mouth_coords[j])
                features.append(distance)
        
        return np.array(features)
    
    def calculate_smile_score(self, features):
        """
        计算笑容评分（0-100分）
        """
        if not self.is_trained:
            # 如果没有训练模型，使用简单的启发式方法
            # 主要基于嘴巴的宽高比
            aspect_ratio = features[2] if len(features) > 2 else 0
            # 简单的线性映射，实际应用中需要根据数据调整
            score = min(100, max(0, aspect_ratio * 500))
            return score
        
        # 使用训练好的模型预测笑容概率
        features_scaled = self.scaler.transform(features.reshape(1, -1))
        smile_probability = self.svm_classifier.predict_proba(features_scaled)[0][1]
        
        # 将概率转换为0-100的分数
        smile_score = smile_probability * 100
        return smile_score
    
    def train_model(self, features, labels):
        """
        训练SVM分类器
        """
        # 标准化特征
        features_scaled = self.scaler.fit_transform(features)
        
        # 训练SVM分类器
        self.svm_classifier.fit(features_scaled, labels)
        self.is_trained = True
        
        # 评估模型
        X_train, X_test, y_train, y_test = train_test_split(
            features_scaled, labels, test_size=0.2, random_state=42
        )
        
        self.svm_classifier.fit(X_train, y_train)
        y_pred = self.svm_classifier.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        print(f"模型准确率: {accuracy:.2f}")
    
    def save_model(self, model_path="smile_model.pkl"):
        """
        保存训练好的模型
        """
        if self.is_trained:
            with open(model_path, 'wb') as f:
                pickle.dump({
                    'classifier': self.svm_classifier,
                    'scaler': self.scaler
                }, f)
            print(f"模型已保存到 {model_path}")
    
    def load_model(self, model_path="smile_model.pkl"):
        """
        加载训练好的模型
        """
        try:
            with open(model_path, 'rb') as f:
                model_data = pickle.load(f)
                self.svm_classifier = model_data['classifier']
                self.scaler = model_data['scaler']
                self.is_trained = True
            print(f"模型已从 {model_path} 加载")
        except FileNotFoundError:
            print("未找到模型文件，将使用默认方法")
    
    def detect_smile_in_image(self, image_path, display=True):
        """
        检测单张图片中的笑容并评分
        """
        # 读取图片
        image = cv2.imread(image_path)
        if image is None:
            print(f"无法读取图片: {image_path}")
            return None
        
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 检测人脸
        faces = self.detector(gray)
        
        if len(faces) == 0:
            print("未检测到人脸")
            return None
        
        results = []
        for i, face in enumerate(faces):
            # 检测关键点
            landmarks = self.predictor(gray, face)
            
            # 提取嘴巴特征
            features = self.extract_mouth_features(gray, landmarks)
            
            # 计算笑容评分
            smile_score = self.calculate_smile_score(features)
            
            # 判断是否微笑
            is_smiling = smile_score > 50  # 阈值可根据需要调整
            
            results.append({
                'face_id': i,
                'smile_score': smile_score,
                'is_smiling': is_smiling,
                'face_rect': (face.left(), face.top(), face.width(), face.height())
            })
            
            # 在图片上绘制结果
            if display:
                # 绘制人脸框
                cv2.rectangle(image, (face.left(), face.top()), 
                             (face.right(), face.bottom()), (0, 255, 0), 2)
                
                # 绘制嘴巴关键点
                for j in self.mouth_points:
                    x = landmarks.part(j).x
                    y = landmarks.part(j).y
                    cv2.circle(image, (x, y), 2, (0, 0, 255), -1)
                
                # 显示笑容评分
                text = f"Smile: {smile_score:.1f}%"
                cv2.putText(image, text, (face.left(), face.top()-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        if display:
            # 显示图片
            cv2.imshow('Smile Detection', image)
            cv2.waitKey(0)
            cv2.destroyAllWindows()
        
        return results
    
    def detect_smile_in_video(self, video_source=0):
        """
        实时检测视频中的笑容
        """
        cap = cv2.VideoCapture(video_source)
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # 转换为灰度图
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # 检测人脸
            faces = self.detector(gray)
            
            for face in faces:
                # 检测关键点
                landmarks = self.predictor(gray, face)
                
                # 提取嘴巴特征
                features = self.extract_mouth_features(gray, landmarks)
                
                # 计算笑容评分
                smile_score = self.calculate_smile_score(features)
                
                # 判断是否微笑
                is_smiling = smile_score > 50
                
                # 绘制人脸框
                cv2.rectangle(frame, (face.left(), face.top()), 
                             (face.right(), face.bottom()), (0, 255, 0), 2)
                
                # 绘制嘴巴关键点
                for j in self.mouth_points:
                    x = landmarks.part(j).x
                    y = landmarks.part(j).y
                    cv2.circle(frame, (x, y), 2, (0, 0, 255), -1)
                
                # 显示笑容评分和状态
                status = "Smiling" if is_smiling else "Not Smiling"
                text = f"{status}: {smile_score:.1f}%"
                cv2.putText(frame, text, (face.left(), face.top()-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            
            # 显示帧
            cv2.imshow('Real-time Smile Detection', frame)
            
            # 按'q'退出
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
        
        cap.release()
        cv2.destroyAllWindows()

# 使用示例
def main():
    # 初始化笑容检测器
    smile_detector = SmileDetector()
    
    # 尝试加载预训练模型（如果有）
    smile_detector.load_model("smile_model.pkl")
    
    # 检测图片中的笑容
    print("=== 图片笑容检测 ===")
    result = smile_detector.detect_smile_in_image("test_face.jpg")
    if result:
        for res in result:
            print(f"人脸 {res['face_id']}: 笑容评分 {res['smile_score']:.1f}% - {'微笑' if res['is_smiling'] else '未微笑'}")
    
    # 实时视频检测
    print("\n=== 实时视频笑容检测 ===")
    print("按 'q' 退出视频检测")
    smile_detector.detect_smile_in_video()

if __name__ == "__main__":
    main()