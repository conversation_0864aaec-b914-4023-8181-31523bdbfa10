
const videoKey = 'icUELAVbpHYj9XDIK98B'
const expireTime = 12000
const crypto = require('crypto');
const fs = require('fs');
const agent = require('superagent');
var fileData = fs.readFileSync('./videoInfo.json', 'utf-8').toString();
let res = JSON.parse(fileData);


const fileDir = './video';
//下载视频保存文
async function downloadsFile(fileUrl,filename) {
    agent.get(fileUrl).pipe(fs.createWriteStream(filename));
}
async function main() {
    for (let i = 0; i < res.length; i++) {
        let url = res[i].video_url;
        await downloadsFile(url, `${fileDir}/${res[i].username}_${res[i].sn}.mp4`);
        console.log('已下载：' + res[i].username + ' ' + res[i].sn,',当前',i);
    }
}
main();

// var fileData = fs.readFileSync('/Users/<USER>/Downloads/loan_application_export.json', 'utf-8').toString();

//   /**
//    * 获得防盗视频url
//    */
//   async function getGuardAgainstTheftVideoUrl(orginalVidelUrl) {
//     const key = videoKey; // 防盗密钥
//     const dir = getMiddleDirPath(orginalVidelUrl); //视频原始 URL 的 PATH 中除去文件名的那部分路径
//     // 过期10位秒级时间戳的十六进制表示结果
//     const t = (parseInt(Date.now() / 1000) + expireTime).toString(16);
//     // sign = md5(KEY + Dir + t + exper + rlimit + us)
//     const signStr = `${key}${dir}${t}`;
//     const md5 = crypto.createHash('md5');
//     md5.update(signStr);
//     const sign = md5.digest('hex'); // 防盗链签名
//     // 防盗链 URL 中 QueryString 的各参数必须按照t、exper、rlimit、us、sign的顺序出现，如果顺序不正确将无法播放视频。
//     const queryStr = `?t=${t}&sign=${sign}`;
//     const guardAgainstTheftVideoUrl = `${orginalVidelUrl}${queryStr}`;
//     return guardAgainstTheftVideoUrl;
//   }

// /** 获得url中间部分的路径 */
// function getMiddleDirPath(url) {
//   let dir = '/';
//   const sp1 = url.split('//');
//   const sp2 = sp1[1].split('/');
//   for (let i = 1; i < sp2.length - 1; i++) {
//     const str = sp2[i];
//     dir = dir + str + '/';
//   }
//   return dir;
// }
// let res = [];
// async function main() {
    
//     let arr = fileData.split('\n');
//     for (let i = 0; i < arr.length; i++) {
//         if(!arr[i]){
//             continue;
//         }
//         let obj = JSON.parse(arr[i]);
//         let url = obj.addons.videoUrl;
//         let newUrl = await getGuardAgainstTheftVideoUrl(url);
//         obj.video_url = newUrl;
//         delete obj.addons;
//         arr[i] = obj;
//     }
//     fs.writeFileSync('./videoInfo.json', JSON.stringify(arr));
// }

// main();